<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>後台按鈕測試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 3px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 後台按鈕功能測試</h1>
        
        <div class="test-section">
            <h3>📋 測試項目</h3>
            <ul>
                <li>✅ 產品管理按鈕跳轉功能</li>
                <li>✅ iframe 載入狀態</li>
                <li>✅ 按鈕事件綁定</li>
                <li>✅ 錯誤處理機制</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🔗 快速連結測試</h3>
            <button class="test-button" onclick="testProductManagement()">
                📦 測試產品管理頁面
            </button>
            <button class="test-button" onclick="testAdminMain()">
                🏠 測試後台主頁
            </button>
            <button class="test-button" onclick="testAPI()">
                🔌 測試產品 API
            </button>
            <div id="linkTestResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>📊 測試結果</h3>
            <div id="testResults" class="result">
                等待測試...
            </div>
        </div>

        <div class="test-section">
            <h3>🔍 除錯資訊</h3>
            <p><strong>當前頁面：</strong> <span id="currentPage"></span></p>
            <p><strong>時間戳：</strong> <span id="timestamp"></span></p>
            <p><strong>瀏覽器：</strong> <span id="userAgent"></span></p>
        </div>
    </div>

    <script>
        // 初始化頁面資訊
        document.getElementById('currentPage').textContent = window.location.href;
        document.getElementById('timestamp').textContent = new Date().toLocaleString();
        document.getElementById('userAgent').textContent = navigator.userAgent;

        // 測試產品管理頁面
        function testProductManagement() {
            const resultDiv = document.getElementById('linkTestResult');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '🔄 正在測試產品管理頁面...';
            
            fetch('./admin/secure_products.html')
                .then(response => {
                    if (response.ok) {
                        resultDiv.innerHTML = '✅ 產品管理頁面可正常訪問<br><a href="./admin/secure_products.html" target="_blank">點擊開啟</a>';
                    } else {
                        resultDiv.innerHTML = '❌ 產品管理頁面無法訪問 (狀態: ' + response.status + ')';
                    }
                })
                .catch(error => {
                    resultDiv.innerHTML = '❌ 測試失敗: ' + error.message;
                });
        }

        // 測試後台主頁
        function testAdminMain() {
            const resultDiv = document.getElementById('linkTestResult');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '🔄 正在測試後台主頁...';
            
            fetch('./admin.php')
                .then(response => {
                    if (response.ok) {
                        resultDiv.innerHTML = '✅ 後台主頁可正常訪問<br><a href="./admin.php" target="_blank">點擊開啟</a>';
                    } else {
                        resultDiv.innerHTML = '❌ 後台主頁無法訪問 (狀態: ' + response.status + ')';
                    }
                })
                .catch(error => {
                    resultDiv.innerHTML = '❌ 測試失敗: ' + error.message;
                });
        }

        // 測試 API
        function testAPI() {
            const resultDiv = document.getElementById('linkTestResult');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '🔄 正在測試產品 API...';
            
            fetch('./api/products_manager.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        resultDiv.innerHTML = `✅ API 正常運作<br>產品數量: ${data.count}<br><a href="./api/products_manager.php" target="_blank">查看 API 回應</a>`;
                    } else {
                        resultDiv.innerHTML = '❌ API 回應錯誤: ' + data.message;
                    }
                })
                .catch(error => {
                    resultDiv.innerHTML = '❌ API 測試失敗: ' + error.message;
                });
        }

        // 自動執行基本測試
        window.addEventListener('load', function() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '🔄 正在執行自動測試...<br>';
            
            // 測試基本功能
            setTimeout(() => {
                let results = [];
                
                // 檢查必要檔案
                const requiredFiles = [
                    './admin.php',
                    './admin/secure_products.html',
                    './api/products_manager.php'
                ];
                
                Promise.all(requiredFiles.map(file => 
                    fetch(file).then(r => ({ file, status: r.status, ok: r.ok }))
                )).then(responses => {
                    responses.forEach(({ file, status, ok }) => {
                        if (ok) {
                            results.push(`✅ ${file} - 正常`);
                        } else {
                            results.push(`❌ ${file} - 錯誤 (${status})`);
                        }
                    });
                    
                    resultsDiv.innerHTML = results.join('<br>') + '<br><br>🎯 <strong>建議：</strong><br>1. 訪問 admin.php 並測試產品管理按鈕<br>2. 檢查瀏覽器控制台的日誌訊息<br>3. 確認按鈕點擊後 iframe 是否正確載入';
                }).catch(error => {
                    resultsDiv.innerHTML = '❌ 自動測試失敗: ' + error.message;
                });
            }, 1000);
        });
    </script>
</body>
</html>
