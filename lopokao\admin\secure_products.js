/**
 * 安全商品管理 JavaScript
 */

const API_BASE = '../api/products_manager.php';
const AUTH_API = '../api/auth.php';
let currentEditingId = null;
let currentUser = null;

// 頁面載入時初始化
document.addEventListener('DOMContentLoaded', function() {
    checkAuth();
    setupEventListeners();
});

// 設定事件監聽器
function setupEventListeners() {
    // 登入表單提交
    document.getElementById('login-form').addEventListener('submit', handleLogin);
    
    // 商品表單提交
    document.getElementById('product-form').addEventListener('submit', handleProductSubmit);
    
    // 庫存表單提交
    document.getElementById('stock-form').addEventListener('submit', handleStockSubmit);
}

// 檢查認證狀態
async function checkAuth() {
    try {
        const response = await fetch(`${AUTH_API}?action=check`);
        const result = await response.json();
        
        if (result.success) {
            currentUser = result.user;
            showMainPage();
            loadProducts();
        } else {
            showLoginPage();
        }
    } catch (error) {
        console.error('認證檢查失敗:', error);
        showLoginPage();
    }
}

// 處理登入
async function handleLogin(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const username = formData.get('username');
    const password = formData.get('password');
    
    try {
        const response = await fetch(`${AUTH_API}?action=login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                username: username,
                password: password
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            currentUser = result.user;
            showMainPage();
            loadProducts();
        } else {
            showLoginError(result.message);
        }
    } catch (error) {
        showLoginError('登入時發生錯誤: ' + error.message);
    }
}

// 登出
async function logout() {
    try {
        await fetch(`${AUTH_API}?action=logout`, {
            method: 'POST'
        });
        
        currentUser = null;
        showLoginPage();
    } catch (error) {
        console.error('登出失敗:', error);
        showLoginPage();
    }
}

// 顯示登入頁面
function showLoginPage() {
    document.getElementById('login-page').classList.remove('hidden');
    document.getElementById('main-page').classList.add('hidden');
    document.getElementById('login-error').classList.add('hidden');
    document.getElementById('login-form').reset();
}

// 顯示主頁面
function showMainPage() {
    document.getElementById('login-page').classList.add('hidden');
    document.getElementById('main-page').classList.remove('hidden');
    
    if (currentUser) {
        document.getElementById('user-name').textContent = currentUser.name;
    }
}

// 顯示登入錯誤
function showLoginError(message) {
    const errorDiv = document.getElementById('login-error');
    errorDiv.textContent = message;
    errorDiv.classList.remove('hidden');
}

// 載入商品列表
async function loadProducts() {
    try {
        showLoading(true);
        
        const response = await fetch(`${API_BASE}?include_inactive=true`);
        const result = await response.json();
        
        if (result.success) {
            renderProductsTable(result.data);
        } else {
            showError('載入商品失敗: ' + result.message);
        }
    } catch (error) {
        showError('載入商品時發生錯誤: ' + error.message);
    } finally {
        showLoading(false);
    }
}

// 渲染商品表格
function renderProductsTable(products) {
    const tbody = document.getElementById('products-tbody');
    const table = document.getElementById('products-table');
    const noProducts = document.getElementById('no-products');
    
    if (products.length === 0) {
        table.classList.add('hidden');
        noProducts.classList.remove('hidden');
        return;
    }
    
    table.classList.remove('hidden');
    noProducts.classList.add('hidden');
    
    tbody.innerHTML = products.map(product => `
        <tr class="border-b hover:bg-gray-50">
            <td class="px-4 py-3 text-sm">${product.sort_order || '-'}</td>
            <td class="px-4 py-3">
                <div class="font-medium text-gray-900">${product.name}</div>
                <div class="text-sm text-gray-500">ID: ${product.id}</div>
            </td>
            <td class="px-4 py-3 text-sm">NT$ ${product.price}</td>
            <td class="px-4 py-3 text-sm">${product.unit || '條'}</td>
            <td class="px-4 py-3">
                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full status-${product.stock_status}">
                    ${getStatusText(product.stock_status)}
                </span>
            </td>
            <td class="px-4 py-3 text-sm">${product.stock_quantity || 0}</td>
            <td class="px-4 py-3">
                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${product.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}">
                    ${product.is_active ? '啟用' : '停用'}
                </span>
            </td>
            <td class="px-4 py-3 text-sm">
                <div class="flex space-x-2">
                    <button onclick="editProduct('${product.id}')" class="text-blue-600 hover:text-blue-800 font-medium">
                        編輯
                    </button>
                    <button onclick="updateStock('${product.id}', '${product.stock_status}', ${product.stock_quantity})" class="text-green-600 hover:text-green-800 font-medium">
                        庫存
                    </button>
                    <button onclick="toggleProduct('${product.id}', ${!product.is_active})" class="text-${product.is_active ? 'red' : 'green'}-600 hover:text-${product.is_active ? 'red' : 'green'}-800 font-medium">
                        ${product.is_active ? '停用' : '啟用'}
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

// 獲取狀態文字
function getStatusText(status) {
    const statusMap = {
        'available': '有庫存',
        'limited': '庫存有限',
        'sold_out': '已完售'
    };
    return statusMap[status] || status;
}

// 顯示新增商品彈窗
function showAddModal() {
    currentEditingId = null;
    document.getElementById('modal-title').textContent = '新增商品';
    document.getElementById('product-form').reset();
    document.getElementById('product-id-input').disabled = false;
    document.getElementById('product-is-active').checked = true;
    document.getElementById('product-modal').classList.remove('hidden');
}

// 編輯商品
async function editProduct(id) {
    try {
        const response = await fetch(`${API_BASE}?path=${id}`);
        const result = await response.json();
        
        if (result.success) {
            currentEditingId = id;
            const product = result.data;
            
            document.getElementById('modal-title').textContent = '編輯商品';
            document.getElementById('product-id').value = product.id;
            document.getElementById('product-id-input').value = product.id;
            document.getElementById('product-id-input').disabled = true;
            document.getElementById('product-name').value = product.name;
            document.getElementById('product-price').value = product.price;
            document.getElementById('product-weight').value = product.weight || '';
            document.getElementById('product-unit').value = product.unit || '條';
            document.getElementById('product-stock-status').value = product.stock_status;
            document.getElementById('product-stock-quantity').value = product.stock_quantity || 0;
            document.getElementById('product-category').value = product.category || 'general';
            document.getElementById('product-sort-order').value = product.sort_order || 1;
            document.getElementById('product-description').value = product.description || '';
            document.getElementById('product-detailed-description').value = product.detailed_description || '';
            document.getElementById('product-shipping-note').value = product.shipping_note || '';
            document.getElementById('product-ingredients').value = Array.isArray(product.ingredients) ? product.ingredients.join('\n') : '';
            document.getElementById('product-image').value = product.image || '';
            document.getElementById('product-is-vegetarian').checked = product.is_vegetarian || false;
            document.getElementById('product-is-active').checked = product.is_active !== false;
            
            document.getElementById('product-modal').classList.remove('hidden');
        } else {
            showError('載入商品資料失敗: ' + result.message);
        }
    } catch (error) {
        showError('載入商品資料時發生錯誤: ' + error.message);
    }
}

// 處理商品表單提交
async function handleProductSubmit(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const data = Object.fromEntries(formData.entries());
    
    // 處理成分陣列
    if (data.ingredients) {
        data.ingredients = data.ingredients.split('\n').filter(item => item.trim());
    }
    
    // 處理數值
    data.price = parseFloat(data.price);
    data.stock_quantity = parseInt(data.stock_quantity) || 0;
    data.sort_order = parseInt(data.sort_order) || 1;
    
    // 處理布林值
    data.is_vegetarian = formData.has('is_vegetarian');
    data.is_active = formData.has('is_active');
    
    try {
        const url = currentEditingId ? `${API_BASE}?path=${currentEditingId}` : API_BASE;
        const method = currentEditingId ? 'PUT' : 'POST';
        
        const response = await fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        
        if (result.success) {
            showSuccess(result.message);
            closeModal();
            loadProducts();
        } else {
            showError(result.message);
        }
    } catch (error) {
        showError('儲存商品時發生錯誤: ' + error.message);
    }
}

// 更新庫存
function updateStock(id, currentStatus, currentQuantity) {
    document.getElementById('stock-product-id').value = id;
    document.getElementById('stock-status').value = currentStatus;
    document.getElementById('stock-quantity').value = currentQuantity || 0;
    document.getElementById('stock-modal').classList.remove('hidden');
}

// 處理庫存表單提交
async function handleStockSubmit(e) {
    e.preventDefault();
    
    const id = document.getElementById('stock-product-id').value;
    const status = document.getElementById('stock-status').value;
    const quantity = parseInt(document.getElementById('stock-quantity').value) || 0;
    
    try {
        const response = await fetch(`${API_BASE}?path=${id}&action=stock`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                status: status,
                quantity: quantity
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            showSuccess('庫存更新成功');
            closeStockModal();
            loadProducts();
        } else {
            showError(result.message);
        }
    } catch (error) {
        showError('更新庫存時發生錯誤: ' + error.message);
    }
}

// 切換商品啟用狀態
async function toggleProduct(id, isActive) {
    try {
        const response = await fetch(`${API_BASE}?path=${id}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                is_active: isActive
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            showSuccess(`商品已${isActive ? '啟用' : '停用'}`);
            loadProducts();
        } else {
            showError(result.message);
        }
    } catch (error) {
        showError('更新商品狀態時發生錯誤: ' + error.message);
    }
}

// 關閉彈窗
function closeModal() {
    document.getElementById('product-modal').classList.add('hidden');
    currentEditingId = null;
}

function closeStockModal() {
    document.getElementById('stock-modal').classList.add('hidden');
}

// 顯示載入狀態
function showLoading(show) {
    const loading = document.getElementById('loading');
    if (show) {
        loading.classList.remove('hidden');
    } else {
        loading.classList.add('hidden');
    }
}

// 顯示成功訊息
function showSuccess(message) {
    alert('✅ ' + message);
}

// 顯示錯誤訊息
function showError(message) {
    alert('❌ ' + message);
}
