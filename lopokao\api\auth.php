<?php
/**
 * 簡單的管理員認證系統
 */

session_start();

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 處理 OPTIONS 請求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

class AuthManager {
    private $configFile;
    private $users;

    public function __construct() {
        $this->configFile = __DIR__ . '/config/admin_users.json';
        $this->loadUsers();
    }

    /**
     * 載入管理員用戶資料
     */
    private function loadUsers() {
        if (file_exists($this->configFile)) {
            $content = file_get_contents($this->configFile);
            $this->users = json_decode($content, true) ?: [];
        } else {
            // 初始化預設管理員帳號
            $this->users = [
                [
                    'username' => 'admin',
                    'password' => password_hash('admin123', PASSWORD_DEFAULT),
                    'name' => '系統管理員',
                    'role' => 'admin',
                    'created_at' => date('Y-m-d H:i:s'),
                    'last_login' => null
                ]
            ];
            $this->saveUsers();
        }
    }

    /**
     * 儲存用戶資料
     */
    private function saveUsers() {
        $configDir = dirname($this->configFile);
        if (!is_dir($configDir)) {
            mkdir($configDir, 0755, true);
        }
        
        return file_put_contents($this->configFile, json_encode($this->users, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    }

    /**
     * 用戶登入
     */
    public function login($username, $password) {
        foreach ($this->users as &$user) {
            if ($user['username'] === $username) {
                if (password_verify($password, $user['password'])) {
                    // 登入成功
                    $_SESSION['admin_logged_in'] = true;
                    $_SESSION['admin_username'] = $username;
                    $_SESSION['admin_name'] = $user['name'];
                    $_SESSION['admin_role'] = $user['role'];
                    $_SESSION['login_time'] = time();
                    
                    // 更新最後登入時間
                    $user['last_login'] = date('Y-m-d H:i:s');
                    $this->saveUsers();
                    
                    return [
                        'success' => true,
                        'message' => '登入成功',
                        'user' => [
                            'username' => $user['username'],
                            'name' => $user['name'],
                            'role' => $user['role']
                        ]
                    ];
                } else {
                    return [
                        'success' => false,
                        'message' => '密碼錯誤'
                    ];
                }
            }
        }
        
        return [
            'success' => false,
            'message' => '用戶不存在'
        ];
    }

    /**
     * 用戶登出
     */
    public function logout() {
        session_destroy();
        return [
            'success' => true,
            'message' => '已登出'
        ];
    }

    /**
     * 檢查登入狀態
     */
    public function checkAuth() {
        if (isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true) {
            // 檢查會話是否過期（24小時）
            if (isset($_SESSION['login_time']) && (time() - $_SESSION['login_time']) > 86400) {
                session_destroy();
                return [
                    'success' => false,
                    'message' => '會話已過期，請重新登入'
                ];
            }
            
            return [
                'success' => true,
                'user' => [
                    'username' => $_SESSION['admin_username'],
                    'name' => $_SESSION['admin_name'],
                    'role' => $_SESSION['admin_role']
                ]
            ];
        }
        
        return [
            'success' => false,
            'message' => '未登入'
        ];
    }

    /**
     * 修改密碼
     */
    public function changePassword($username, $oldPassword, $newPassword) {
        foreach ($this->users as &$user) {
            if ($user['username'] === $username) {
                if (password_verify($oldPassword, $user['password'])) {
                    $user['password'] = password_hash($newPassword, PASSWORD_DEFAULT);
                    $this->saveUsers();
                    
                    return [
                        'success' => true,
                        'message' => '密碼修改成功'
                    ];
                } else {
                    return [
                        'success' => false,
                        'message' => '舊密碼錯誤'
                    ];
                }
            }
        }
        
        return [
            'success' => false,
            'message' => '用戶不存在'
        ];
    }
}

// API 端點處理
try {
    $auth = new AuthManager();
    $method = $_SERVER['REQUEST_METHOD'];
    $action = $_GET['action'] ?? '';

    switch ($method) {
        case 'POST':
            $input = file_get_contents('php://input');
            $data = json_decode($input, true);
            
            if (empty($data)) {
                $data = $_POST;
            }
            
            switch ($action) {
                case 'login':
                    $username = $data['username'] ?? '';
                    $password = $data['password'] ?? '';
                    
                    if (empty($username) || empty($password)) {
                        $result = [
                            'success' => false,
                            'message' => '請輸入用戶名和密碼'
                        ];
                    } else {
                        $result = $auth->login($username, $password);
                    }
                    break;
                    
                case 'logout':
                    $result = $auth->logout();
                    break;
                    
                case 'change_password':
                    $username = $_SESSION['admin_username'] ?? '';
                    $oldPassword = $data['old_password'] ?? '';
                    $newPassword = $data['new_password'] ?? '';
                    
                    if (empty($oldPassword) || empty($newPassword)) {
                        $result = [
                            'success' => false,
                            'message' => '請輸入舊密碼和新密碼'
                        ];
                    } else {
                        $result = $auth->changePassword($username, $oldPassword, $newPassword);
                    }
                    break;
                    
                default:
                    $result = [
                        'success' => false,
                        'message' => '無效的操作'
                    ];
                    break;
            }
            break;
            
        case 'GET':
            if ($action === 'check') {
                $result = $auth->checkAuth();
            } else {
                $result = [
                    'success' => false,
                    'message' => '無效的操作'
                ];
            }
            break;
            
        default:
            http_response_code(405);
            $result = [
                'success' => false,
                'message' => '不支援的請求方法'
            ];
            break;
    }

    echo json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => '伺服器錯誤: ' . $e->getMessage()
    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
}
?>
