<?php
/**
 * 統一後台管理入口
 * 響應式設計，支援桌面和行動裝置
 */

session_start();

// 檢查登入狀態
$isLoggedIn = isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;

// 如果沒有登入，重定向到登入頁面
if (!$isLoggedIn) {
    header('Location: admin-redirect.php');
    exit;
}

// 檢測當前是否為 HTTPS 訪問
$isHttps = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ||
           isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https' ||
           isset($_SERVER['HTTP_X_FORWARDED_SSL']) && $_SERVER['HTTP_X_FORWARDED_SSL'] === 'on' ||
           strpos($_SERVER['HTTP_HOST'], '767780.xyz') !== false;

// 後台系統 URL - 根據訪問協議智能選擇
if ($isHttps) {
    // HTTPS 訪問時，必須使用 HTTPS 後台地址（避免混合內容錯誤）
    $possibleUrls = [
        'https://node.767780.xyz/',   // Cloudflare HTTPS 後台
    ];
    $backendUrl = 'https://node.767780.xyz/'; // 預設使用 Cloudflare HTTPS
} else {
    // HTTP 訪問時，使用 HTTP 地址
    $possibleUrls = [
        'http://localhost:8080',      // 本機端口
        'http://************:8080'    // 外網 IP
    ];
    $backendUrl = 'http://localhost:8080'; // 預設使用本機
}

// 根據 HTTPS 狀態和訪問環境選擇測試 URL
$isLocalAccess = (
    $_SERVER['HTTP_HOST'] === 'localhost' ||
    $_SERVER['HTTP_HOST'] === '127.0.0.1' ||
    strpos($_SERVER['HTTP_HOST'], '192.168.') === 0 ||
    strpos($_SERVER['HTTP_HOST'], '10.') === 0
);

if ($isHttps) {
    // HTTPS 訪問時，只測試 HTTPS 地址
    $testUrls = $possibleUrls; // 使用上面定義的 HTTPS URLs
} else {
    // HTTP 訪問時，根據環境選擇
    if ($isLocalAccess) {
        $testUrls = [
            'http://localhost:8080',
            'http://************:8080'
        ];
    } else {
        $testUrls = [
            'http://************:8080',
            'http://localhost:8080'
        ];
    }
}

// 檢測可用的 URL
foreach ($testUrls as $url) {
    $context = stream_context_create([
        'http' => [
            'timeout' => 3,
            'method' => 'HEAD',
            'ignore_errors' => true
        ]
    ]);

    $result = @file_get_contents($url, false, $context);
    if ($result !== false || strpos($http_response_header[0], '200') !== false) {
        $backendUrl = $url;
        break;
    }
}

// 記錄檢測結果
error_log("後台 URL 檢測結果: $backendUrl (訪問來源: {$_SERVER['HTTP_HOST']})");
?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>蘿蔔糕訂購系統 - 管理後台</title>
    <link rel="icon" href="https://app.trickle.so/storage/app/LOGO-2.webp">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="admin-mobile-styles.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8fafc;
            min-height: 100vh;
            overflow: hidden;
        }

        /* 頂部導航欄 */
        .top-navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            height: 60px;
        }

        .navbar-left {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .navbar-title {
            font-size: 18px;
            font-weight: 600;
        }

        .navbar-right {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .nav-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-1px);
        }

        .nav-btn.danger {
            background: rgba(220, 53, 69, 0.8);
            border-color: rgba(220, 53, 69, 0.9);
        }

        .nav-btn.danger:hover {
            background: rgba(220, 53, 69, 0.9);
        }

        /* iframe 容器 */
        .iframe-container {
            position: fixed;
            top: 50px; /* 移除頂部導航後，iframe 容器向上移動 */
            left: 0;
            right: 0;
            bottom: 0;
            background: white;
        }

        .backend-iframe {
            width: 100%;
            height: 100%;
            border: none;
            background: white;
        }

        /* 載入動畫 */
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: white;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 10;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            font-size: 18px;
            color: #333;
            margin-bottom: 10px;
        }

        .loading-subtext {
            font-size: 14px;
            color: #666;
        }

        /* 錯誤訊息 */
        .error-message {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #f8d7da;
            color: #721c24;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #f5c6cb;
            text-align: center;
            display: none;
            z-index: 20;
            max-width: 400px;
        }

        /* 響應式設計 */
        @media (max-width: 768px) {
            .navbar-title {
                font-size: 16px;
            }

            .nav-btn {
                padding: 6px 12px;
                font-size: 12px;
            }

            .nav-btn span {
                display: none;
            }

            .navbar-right {
                gap: 8px;
            }

            .top-navbar {
                padding: 10px 15px;
                height: 55px;
            }

            .iframe-container {
                top: 0; /* 移除頂部導航後，iframe 容器向上移動 */
            }
        }

        @media (max-width: 480px) {
            .navbar-title {
                font-size: 14px;
            }

            .nav-btn {
                padding: 5px 10px;
                font-size: 11px;
            }

            .top-navbar {
                padding: 8px 12px;
                height: 50px;
            }

            .iframe-container {
                top: 0; /* 移除頂部導航後，iframe 容器向上移動 */
            }
        }

        /* iframe 樣式 */
        .backend-iframe {
            width: 100%;
            height: 100%;
            border: none;
            background: white;
        }

        /* 確保 iframe 內容不會被遮擋 */
        .iframe-wrapper {
            position: relative;
            width: 100%;
            height: 100%;
            overflow: hidden;
        }
    </style>
</head>
<body>
    <!-- 頂部導航欄 -->
    <div class="top-navbar">
        <div class="navbar-left">
            <i class="fas fa-tachometer-alt"></i>
            <div class="navbar-title">管理後台</div>
            <button id="switchToProductsBtn" class="nav-btn">
                <i class="fas fa-box"></i>
                <span>產品管理</span>
            </button>
            <button id="switchToOrdersBtn" class="nav-btn">
                <i class="fas fa-file-invoice"></i>
                <span>訂單</span>
            </button>
            <button id="switchToCustomersBtn" class="nav-btn">
                <i class="fas fa-users"></i>
                <span>客戶</span>
            </button>
        </div>
        <div class="navbar-right">
            <button class="nav-btn" onclick="openManualOrderForm()">
                <i class="fas fa-edit"></i>
                <span>手抄單</span>
            </button>
            <button class="nav-btn" onclick="openDeliverySettings()">
                <i class="fas fa-calendar-alt"></i>
                <span>到貨日設定</span>
            </button>
            <button class="nav-btn" onclick="refreshIframe()">
                <i class="fas fa-refresh"></i>
                <span>重新載入</span>
            </button>
            <a href="admin-redirect.php?action=logout" class="nav-btn danger">
                <i class="fas fa-sign-out-alt"></i>
                <span>登出</span>
            </a>
        </div>
    </div>

    <!-- iframe 容器 -->
    <div class="iframe-container">
        <div class="loading-overlay" id="loadingOverlay">
            <div class="loading-spinner"></div>
            <div class="loading-text">正在載入管理後台...</div>
            <div class="loading-subtext">請稍候，系統正在啟動中</div>
        </div>

        <div class="error-message" id="errorMessage">
            <strong>載入失敗！</strong> 無法連接到後台系統。
            <br><br>
            <div style="display: flex; gap: 10px; justify-content: center; flex-wrap: wrap;">
                <button onclick="reloadIframe()" style="background: #dc3545; color: white; border: none; padding: 8px 15px; border-radius: 4px; cursor: pointer;">
                    <i class="fas fa-refresh"></i> 重新載入
                </button>
                <button onclick="tryAlternativeBackendUrl()" style="background: #007bff; color: white; border: none; padding: 8px 15px; border-radius: 4px; cursor: pointer;">
                    <i class="fas fa-exchange-alt"></i> 切換地址
                </button>
            </div>
            <br>
            <small style="color: #666;">
                當前地址: <span id="currentUrl"><?php echo $backendUrl; ?></span>
            </small>
        </div>

        <iframe
            id="backendIframe"
            class="backend-iframe"
            src="<?php echo $backendUrl; ?>">
        </iframe>
    </div>

    <script>
        let iframeLoadTimeout;
        let isIframeLoaded = false;

        // 當前後台 URL 和協議檢測
        const currentBackendUrl = '<?php echo $backendUrl; ?>';
        const isHttps = window.location.protocol === 'https:';
        const isCloudflare = window.location.hostname.includes('767780.xyz');

        //console.log('🌐 當前後台 URL:', currentBackendUrl);
        //console.log('🔒 當前協議:', window.location.protocol);
        //console.log('☁️ Cloudflare 訪問:', isCloudflare);
        //console.log('🔧 HTTPS 模式:', isHttps ? '是（將使用 HTTPS 後台）' : '否（將使用 HTTP 後台）');

        const backendIframe = document.getElementById('backendIframe');
        const switchToProductsBtn = document.getElementById('switchToProductsBtn');
        const switchToOrdersBtn = document.getElementById('switchToOrdersBtn');
        const switchToCustomersBtn = document.getElementById('switchToCustomersBtn');

        if (switchToProductsBtn && backendIframe) {
            switchToProductsBtn.addEventListener('click', () => {
                if (backendIframe.contentWindow) {
                    backendIframe.contentWindow.postMessage({ type: 'SET_PAGE_MODE', mode: 'products' }, '*');
                } else {
                    console.warn('無法訪問 iframe contentWindow (switchToProductsBtn)');
                }
            });
        }

        if (switchToOrdersBtn && backendIframe) {
            switchToOrdersBtn.addEventListener('click', () => {
                if (backendIframe.contentWindow) {
                    backendIframe.contentWindow.postMessage({ type: 'SET_PAGE_MODE', mode: 'orders' }, '*');
                } else {
                    console.warn('無法訪問 iframe contentWindow (switchToOrdersBtn)');
                }
            });
        }

        if (switchToCustomersBtn && backendIframe) {
            switchToCustomersBtn.addEventListener('click', () => {
                if (backendIframe.contentWindow) {
                    backendIframe.contentWindow.postMessage({ type: 'SET_PAGE_MODE', mode: 'customers' }, '*');
                } else {
                    console.warn('無法訪問 iframe contentWindow (switchToCustomersBtn)');
                }
            });
        }

        // 開啟手抄單
        function openManualOrderForm() {
            window.open('https://767780.xyz/pos8-test.php', '_blank', 'width=800,height=900,scrollbars=yes,resizable=yes');
        }
 
        // 開啟到貨日設定
        function openDeliverySettings() {
            // 在新視窗開啟，避免影響主要後台
            window.open('http://************/lopokao/admin-delivery-settings.php', '_blank', 'width=800,height=900,scrollbars=yes,resizable=yes');
        }
 
        // 重新載入 iframe（別名函數）
        function reloadIframe() {
            refreshIframe();
        }

        // 重新載入 iframe
        function refreshIframe() {
            const iframe = document.getElementById('backendIframe');
            const loadingOverlay = document.getElementById('loadingOverlay');
            const errorMessage = document.getElementById('errorMessage');

            // 顯示載入動畫
            loadingOverlay.style.display = 'flex';
            errorMessage.style.display = 'none';
            isIframeLoaded = false;

            // 重新載入
            iframe.src = iframe.src;

            // 設定超時
            setupLoadTimeout();
        }

        // 嘗試切換後台 URL
        function tryAlternativeBackendUrl() {
            const iframe = document.getElementById('backendIframe');

            // 根據當前頁面協議選擇備用 URL
            const isHttps = window.location.protocol === 'https:';
            const alternativeUrls = isHttps ? [
                'https://node.767780.xyz/',   // Cloudflare HTTPS 後台
                'http://************:8080/', // HTTPS 直接 IP
            ] : [
                'http://localhost:8080',      // 本機端口
                'http://************:8080'    // 外網 IP
            ];

            // 找到當前 URL 的索引
            let currentIndex = alternativeUrls.findIndex(url => iframe.src.includes(url));

            // 切換到下一個 URL
            let nextIndex = (currentIndex + 1) % alternativeUrls.length;
            let newUrl = alternativeUrls[nextIndex];

            console.log(`🔄 切換後台 URL: ${iframe.src} → ${newUrl}`);

            // 更新 iframe src
            iframe.src = newUrl;

            // 更新顯示的當前地址
            const currentUrlSpan = document.getElementById('currentUrl');
            if (currentUrlSpan) {
                currentUrlSpan.textContent = newUrl;
            }

            // 顯示載入動畫
            document.getElementById('loadingOverlay').style.display = 'flex';
            document.getElementById('errorMessage').style.display = 'none';
            isIframeLoaded = false;

            // 設定超時
            setupLoadTimeout();

            // 顯示切換提示
            showUrlSwitchNotice(newUrl);
        }

        // 顯示 URL 切換提示
        function showUrlSwitchNotice(newUrl) {
            const notice = document.createElement('div');
            notice.style.cssText = `
                position: fixed;
                top: 70px;
                left: 10px;
                right: 10px;
                background: #d1ecf1;
                border: 1px solid #bee5eb;
                border-radius: 6px;
                padding: 12px;
                color: #0c5460;
                font-size: 14px;
                z-index: 1000;
                box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            `;

            notice.innerHTML = `
                <div style="display: flex; align-items: center; gap: 8px;">
                    <i class="fas fa-exchange-alt"></i>
                    <span>已切換到後台地址: ${newUrl}</span>
                    <button onclick="this.parentElement.parentElement.remove()"
                            style="margin-left: auto; background: none; border: none; color: #0c5460; cursor: pointer;">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;

            document.body.appendChild(notice);

            // 5秒後自動移除
            setTimeout(() => {
                if (notice.parentElement) {
                    notice.remove();
                }
            }, 5000);
        }

        // 設定載入超時
        function setupLoadTimeout() {
            clearTimeout(iframeLoadTimeout);
            iframeLoadTimeout = setTimeout(() => {
                if (!isIframeLoaded) {
                    document.getElementById('loadingOverlay').style.display = 'none';
                    document.getElementById('errorMessage').style.display = 'block';

                    // 自動嘗試切換到備用地址
                    const iframe = document.getElementById('backendIframe');
                    const isHttps = window.location.protocol === 'https:';
                    const fallbackUrl = isHttps ? 'https://node.767780.xyz/' : 'http://localhost:8080';

                    if (!iframe.src.includes(fallbackUrl)) {
                        console.log(`🔄 載入超時，自動嘗試備用地址: ${fallbackUrl}`);
                        setTimeout(() => {
                            iframe.src = fallbackUrl;
                            document.getElementById('currentUrl').textContent = fallbackUrl;
                            showUrlSwitchNotice(fallbackUrl);
                        }, 2000);
                    }
                }
            }, 8000); // 8秒超時，給自動切換留時間
        }

        // iframe 載入事件
        document.getElementById('backendIframe').addEventListener('load', function() {
            isIframeLoaded = true;
            clearTimeout(iframeLoadTimeout);

            setTimeout(() => {
                document.getElementById('loadingOverlay').style.display = 'none';

                // 由於舊後台的頂部按鈕導航將被移除，不再需要注入按鈕樣式修正
                // injectButtonStyles();

                // 行動裝置錯誤檢測
                if (window.innerWidth <= 768) {
                    detectMobileErrors();
                }
            }, 1000); // 延遲1秒隱藏載入動畫
        });

        // iframe 錯誤事件
        document.getElementById('backendIframe').addEventListener('error', function() {
            clearTimeout(iframeLoadTimeout);
            document.getElementById('loadingOverlay').style.display = 'none';
            document.getElementById('errorMessage').style.display = 'block';
        });

        // 由於舊後台的頂部按鈕導航將被移除，
        // injectButtonStyles, injectStylesViaPostMessage, applyCSSOverride, showCrossOriginNotice,
        // forceButtonAlignment, setupButtonMonitoring, debugButtonStructure 這些函數不再需要。
        // 為了簡潔起見，這裡將它們完全移除。

        // 行動裝置錯誤檢測
        function detectMobileErrors() {
            try {
                const iframe = document.getElementById('backendIframe');

                // 檢查跨域限制
                let iframeDoc;
                try {
                    iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                } catch (crossOriginError) {
                    console.warn('🚫 跨域限制：無法檢測行動裝置錯誤');
                    showMobileError('由於跨域限制，無法自動檢測後台狀態');
                    return;
                }

                // 檢測常見的行動裝置載入問題
                setTimeout(() => {
                    // 檢查是否有內容載入
                    const bodyContent = iframeDoc.body.innerHTML.trim();
                    if (bodyContent.length < 100) {
                        console.error('🚨 行動裝置檢測：內容載入不完整');
                        showMobileError('內容載入不完整，可能是網路問題');
                        return;
                    }

                    // 檢查是否有錯誤訊息
                    const errorElements = iframeDoc.querySelectorAll('.error, .alert-danger, [class*="error"]');
                    if (errorElements.length > 0) {
                        console.error('🚨 行動裝置檢測：發現錯誤元素');
                        showMobileError('後台系統回報錯誤，請檢查網路連線');
                        return;
                    }

                    // 檢查是否有載入失敗的圖片或資源
                    const images = iframeDoc.querySelectorAll('img');
                    let brokenImages = 0;
                    images.forEach(img => {
                        if (!img.complete || img.naturalHeight === 0) {
                            brokenImages++;
                        }
                    });

                    if (brokenImages > images.length * 0.5) {
                        console.warn('⚠️ 行動裝置檢測：多數圖片載入失敗');
                        showMobileError('部分資源載入失敗，可能影響顯示效果');
                    }

                    //console.log('✅ 行動裝置檢測完成，未發現嚴重問題');

                }, 2000); // 等待2秒讓內容完全載入

            } catch (error) {
                console.error('🚨 行動裝置錯誤檢測失敗:', error);
                showMobileError('無法檢測後台狀態，可能是跨域限制');
            }
        }

        // 顯示行動裝置錯誤訊息
        function showMobileError(message) {
            // 創建行動裝置專用的錯誤提示
            const errorDiv = document.createElement('div');
            errorDiv.style.cssText = `
                position: fixed;
                top: 70px;
                left: 10px;
                right: 10px;
                background: #fee2e2;
                border: 1px solid #fca5a5;
                border-radius: 8px;
                padding: 12px;
                color: #991b1b;
                font-size: 14px;
                z-index: 1000;
                box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            `;

            errorDiv.innerHTML = `
                <div style="display: flex; align-items: center; gap: 8px;">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>${message}</span>
                    <button onclick="this.parentElement.parentElement.remove()"
                            style="margin-left: auto; background: none; border: none; color: #991b1b; font-size: 16px; cursor: pointer;">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;

            document.body.appendChild(errorDiv);

            // 5秒後自動移除
            setTimeout(() => {
                if (errorDiv.parentElement) {
                    errorDiv.remove();
                }
            }, 5000);
        }

        // 重新載入函數（供錯誤訊息按鈕使用）
        function reloadIframe() {
            refreshIframe();
        }

        // 頁面載入時設定超時
        document.addEventListener('DOMContentLoaded', function() {
            setupLoadTimeout();
        });
    </script>
</body>
</html>
