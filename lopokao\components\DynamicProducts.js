/**
 * 動態商品展示組件
 * 從 API 載入商品資料並動態顯示
 */

function DynamicProducts() {
    const [products, setProducts] = React.useState([]);
    const [loading, setLoading] = React.useState(true);
    const [error, setError] = React.useState(null);

    // 載入商品資料
    React.useEffect(() => {
        loadProducts();
    }, []);

    const loadProducts = async () => {
        try {
            setLoading(true);
            setError(null);
            
            const response = await fetch('./api/products_manager.php');
            const result = await response.json();
            
            if (result.success) {
                // 只顯示啟用的商品，按排序順序排列
                const activeProducts = result.data
                    .filter(product => product.is_active)
                    .sort((a, b) => (a.sort_order || 999) - (b.sort_order || 999));
                setProducts(activeProducts);
            } else {
                setError('載入商品失敗: ' + result.message);
            }
        } catch (err) {
            setError('載入商品時發生錯誤: ' + err.message);
            console.error('Product loading error:', err);
        } finally {
            setLoading(false);
        }
    };

    // 格式化成分顯示
    const formatIngredients = (ingredients) => {
        if (Array.isArray(ingredients)) {
            return ingredients.join('、');
        }
        return ingredients || '';
    };

    // 獲取庫存狀態標籤
    const getStockStatusBadge = (product) => {
        switch (product.stock_status) {
            case 'sold_out':
                return (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                        已完售
                    </span>
                );
            case 'limited':
                return (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                        庫存有限
                    </span>
                );
            default:
                return null;
        }
    };

    if (loading) {
        return (
            <section className="py-16 bg-white" data-name="products">
                <div className="container mx-auto px-4">
                    <div className="text-center mb-12">
                        <h2 className="text-3xl font-bold text-gray-800 mb-4">我們的商品</h2>
                        <p className="text-gray-600 max-w-2xl mx-auto">
                            傳承古早味，堅持手工製作，每一口都是滿滿的用心與美味
                        </p>
                    </div>
                    
                    <div className="flex items-center justify-center py-12">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-500"></div>
                        <span className="ml-3 text-gray-600">載入商品中...</span>
                    </div>
                </div>
            </section>
        );
    }

    if (error) {
        return (
            <section className="py-16 bg-white" data-name="products">
                <div className="container mx-auto px-4">
                    <div className="text-center mb-12">
                        <h2 className="text-3xl font-bold text-gray-800 mb-4">我們的商品</h2>
                        <p className="text-gray-600 max-w-2xl mx-auto">
                            傳承古早味，堅持手工製作，每一口都是滿滿的用心與美味
                        </p>
                    </div>
                    
                    <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
                        <div className="flex items-center justify-between">
                            <div>
                                <h3 className="text-red-800 font-medium">載入商品失敗</h3>
                                <p className="text-red-600 text-sm mt-1">{error}</p>
                            </div>
                            <button
                                onClick={loadProducts}
                                className="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded text-sm transition-colors"
                            >
                                重試
                            </button>
                        </div>
                    </div>
                </div>
            </section>
        );
    }

    if (products.length === 0) {
        return (
            <section className="py-16 bg-white" data-name="products">
                <div className="container mx-auto px-4">
                    <div className="text-center mb-12">
                        <h2 className="text-3xl font-bold text-gray-800 mb-4">我們的商品</h2>
                        <p className="text-gray-600 max-w-2xl mx-auto">
                            傳承古早味，堅持手工製作，每一口都是滿滿的用心與美味
                        </p>
                    </div>
                    
                    <div className="bg-gray-50 border border-gray-200 rounded-lg p-12 text-center">
                        <p className="text-gray-600">目前沒有可展示的商品</p>
                        <button
                            onClick={loadProducts}
                            className="mt-3 bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded transition-colors"
                        >
                            重新載入
                        </button>
                    </div>
                </div>
            </section>
        );
    }

    return (
        <section className="py-16 bg-white" data-name="products">
            <div className="container mx-auto px-4">
                <div className="text-center mb-12">
                    <h2 className="text-3xl font-bold text-gray-800 mb-4">我們的商品</h2>
                    <p className="text-gray-600 max-w-2xl mx-auto">
                        傳承古早味，堅持手工製作，每一口都是滿滿的用心與美味
                    </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    {products.map((product) => (
                        <div key={product.id} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                            <div className="relative">
                                <img 
                                    src={product.image} 
                                    alt={product.name}
                                    className="w-full h-64 object-cover"
                                    style={{ height: '80%' }}
                                />
                                {getStockStatusBadge(product) && (
                                    <div className="absolute top-3 right-3">
                                        {getStockStatusBadge(product)}
                                    </div>
                                )}
                            </div>
                            
                            <div className="p-6">
                                <h3 className="text-xl font-bold text-gray-800 mb-2">{product.name}</h3>
                                <p className="text-gray-600 mb-3">{product.description}</p>
                                
                                <div className="mb-4">
                                    <h4 className="text-sm font-semibold text-gray-700 mb-2">成分：</h4>
                                    <p className="text-sm text-gray-600">{formatIngredients(product.ingredients)}</p>
                                </div>
                                
                                <div className="flex justify-between items-center mb-4">
                                    <span className="text-2xl font-bold text-red-600">
                                        NT$ {product.price}
                                        {product.unit && <span className="text-sm text-gray-500">/{product.unit}</span>}
                                    </span>
                                    {product.weight && (
                                        <span className="text-sm text-gray-500">{product.weight}</span>
                                    )}
                                </div>
                                
                                {product.shipping_note && (
                                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
                                        <p className="text-sm text-blue-700">
                                            💡 {product.shipping_note}
                                        </p>
                                    </div>
                                )}
                                
                                {product.detailed_description && (
                                    <p className="text-sm text-gray-600 mb-4">{product.detailed_description}</p>
                                )}
                                
                                <div className="flex items-center justify-between">
                                    {product.is_vegetarian && (
                                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            🌱 素食
                                        </span>
                                    )}
                                    
                                    {product.stock_status === 'sold_out' ? (
                                        <button 
                                            disabled
                                            className="bg-gray-300 text-gray-500 px-4 py-2 rounded-lg cursor-not-allowed"
                                        >
                                            已完售
                                        </button>
                                    ) : (
                                        <button 
                                            onClick={() => {
                                                // 滾動到訂購表單
                                                const orderForm = document.querySelector('[data-name="order-form"]');
                                                if (orderForm) {
                                                    orderForm.scrollIntoView({ behavior: 'smooth' });
                                                }
                                            }}
                                            className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors"
                                        >
                                            立即訂購
                                        </button>
                                    )}
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
                

            </div>
        </section>
    );
}

// 導出給其他組件使用
window.DynamicProducts = DynamicProducts;
