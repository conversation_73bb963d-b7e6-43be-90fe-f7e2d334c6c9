<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>後台管理系統 - 融氏古早味蘿蔔糕</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .status-available { @apply bg-green-100 text-green-800; }
        .status-limited { @apply bg-yellow-100 text-yellow-800; }
        .status-sold_out { @apply bg-red-100 text-red-800; }
        .login-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 登入頁面 -->
    <div id="login-page" class="login-container">
        <div class="bg-white rounded-lg shadow-xl p-8 w-full max-w-md mx-4">
            <div class="text-center mb-8">
                <h1 class="text-2xl font-bold text-gray-800 mb-2">商品管理系統</h1>
                <p class="text-gray-600">請登入以繼續</p>
            </div>
            
            <form id="login-form" class="space-y-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">用戶名</label>
                    <input type="text" id="username" name="username" required 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                           placeholder="請輸入用戶名">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">密碼</label>
                    <input type="password" id="password" name="password" required 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                           placeholder="請輸入密碼">
                </div>
                
                <button type="submit" class="w-full bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-md transition-colors">
                    登入
                </button>
            </form>
            
            <div id="login-error" class="hidden mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded"></div>
        </div>
    </div>

    <!-- 主要管理介面 -->
    <div id="main-page" class="hidden">
        <div class="container mx-auto px-4 py-8">
            <div class="bg-white rounded-lg shadow-lg">
                <!-- 標題列 -->
                <div class="flex justify-between items-center p-6 border-b">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800">商品管理</h1>
                        <p class="text-sm text-gray-600 mt-1">歡迎，<span id="user-name"></span></p>
                    </div>
                    <div class="flex space-x-3">
                        <button onclick="showAddModal()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors">
                            ➕ 新增商品
                        </button>
                        <button onclick="logout()" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors">
                            登出
                        </button>
                    </div>
                </div>

                <!-- 商品列表 -->
                <div class="p-6">
                    <div id="loading" class="text-center py-8">
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
                        <p class="mt-2 text-gray-600">載入中...</p>
                    </div>

                    <div id="products-table" class="hidden">
                        <div class="overflow-x-auto">
                            <table class="w-full table-auto">
                                <thead>
                                    <tr class="bg-gray-50">
                                        <th class="px-4 py-3 text-left text-sm font-medium text-gray-500">排序</th>
                                        <th class="px-4 py-3 text-left text-sm font-medium text-gray-500">商品名稱</th>
                                        <th class="px-4 py-3 text-left text-sm font-medium text-gray-500">價格</th>
                                        <th class="px-4 py-3 text-left text-sm font-medium text-gray-500">單位</th>
                                        <th class="px-4 py-3 text-left text-sm font-medium text-gray-500">庫存狀態</th>
                                        <th class="px-4 py-3 text-left text-sm font-medium text-gray-500">庫存數量</th>
                                        <th class="px-4 py-3 text-left text-sm font-medium text-gray-500">狀態</th>
                                        <th class="px-4 py-3 text-left text-sm font-medium text-gray-500">操作</th>
                                    </tr>
                                </thead>
                                <tbody id="products-tbody">
                                    <!-- 動態載入商品資料 -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div id="no-products" class="hidden text-center py-8">
                        <p class="text-gray-500">尚無商品資料</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增/編輯商品彈窗 -->
    <div id="product-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center z-50">
        <div class="bg-white rounded-lg w-full max-w-2xl mx-4 max-h-screen overflow-y-auto">
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h2 id="modal-title" class="text-xl font-bold">新增商品</h2>
                    <button onclick="closeModal()" class="text-gray-500 hover:text-gray-700 text-2xl">&times;</button>
                </div>

                <form id="product-form" class="space-y-4">
                    <input type="hidden" id="product-id" name="id">
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">商品 ID</label>
                            <input type="text" id="product-id-input" name="id" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">商品名稱</label>
                            <input type="text" id="product-name" name="name" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">價格 (NT$)</label>
                            <input type="number" id="product-price" name="price" min="0" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">重量</label>
                            <input type="text" id="product-weight" name="weight" placeholder="例如: 1500g" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">單位</label>
                            <select id="product-unit" name="unit" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="條">條</option>
                                <option value="個">個</option>
                                <option value="盒">盒</option>
                                <option value="包">包</option>
                                <option value="份">份</option>
                                <option value="組">組</option>
                            </select>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">庫存狀態</label>
                            <select id="product-stock-status" name="stock_status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="available">有庫存</option>
                                <option value="limited">庫存有限</option>
                                <option value="sold_out">已完售</option>
                            </select>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">庫存數量</label>
                            <input type="number" id="product-stock-quantity" name="stock_quantity" min="0" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">分類</label>
                            <select id="product-category" name="category" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="traditional">傳統口味</option>
                                <option value="premium">精選系列</option>
                                <option value="seasonal">季節限定</option>
                                <option value="general">一般商品</option>
                            </select>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">排序順序</label>
                            <input type="number" id="product-sort-order" name="sort_order" min="1" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">商品描述</label>
                        <textarea id="product-description" name="description" rows="2" placeholder="例如: 1條約1500克(+-5%)" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">詳細描述</label>
                        <textarea id="product-detailed-description" name="detailed_description" rows="3" placeholder="詳細的商品介紹..." class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">運費說明</label>
                        <input type="text" id="product-shipping-note" name="shipping_note" placeholder="例如: 購買二條即可免運費" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">成分 (每行一個)</label>
                        <textarea id="product-ingredients" name="ingredients" rows="3" placeholder="水&#10;在來米&#10;白蘿蔔" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">圖片 URL</label>
                        <input type="url" id="product-image" name="image" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" id="product-is-vegetarian" name="is_vegetarian" class="mr-2">
                        <label for="product-is-vegetarian" class="text-sm font-medium text-gray-700">素食</label>
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" id="product-is-active" name="is_active" class="mr-2" checked>
                        <label for="product-is-active" class="text-sm font-medium text-gray-700">啟用</label>
                    </div>
                    
                    <div class="flex justify-end space-x-3 pt-4">
                        <button type="button" onclick="closeModal()" class="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors">
                            取消
                        </button>
                        <button type="submit" class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors">
                            儲存
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 快速庫存更新彈窗 -->
    <div id="stock-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center z-50">
        <div class="bg-white rounded-lg w-full max-w-md mx-4">
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-bold">更新庫存</h2>
                    <button onclick="closeStockModal()" class="text-gray-500 hover:text-gray-700 text-2xl">&times;</button>
                </div>

                <form id="stock-form" class="space-y-4">
                    <input type="hidden" id="stock-product-id">
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">庫存狀態</label>
                        <select id="stock-status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="available">有庫存</option>
                            <option value="limited">庫存有限</option>
                            <option value="sold_out">已完售</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">庫存數量</label>
                        <input type="number" id="stock-quantity" min="0" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    
                    <div class="flex justify-end space-x-3 pt-4">
                        <button type="button" onclick="closeStockModal()" class="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors">
                            取消
                        </button>
                        <button type="submit" class="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors">
                            更新
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="config.js"></script>
    <script src="secure_products.js"></script>
</body>
</html>
