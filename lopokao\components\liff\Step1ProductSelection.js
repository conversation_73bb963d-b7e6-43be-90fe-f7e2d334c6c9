/**
 * LIFF 訂購系統 - 步驟1：產品選擇
 */

function Step1ProductSelection({ orderData, setOrderData, onNext }) {
    const [products, setProducts] = React.useState([]);
    const [loading, setLoading] = React.useState(true);
    const [error, setError] = React.useState(null);

    // 載入產品數據
    React.useEffect(() => {
        loadProducts();
    }, []);

    const loadProducts = async () => {
        try {
            setLoading(true);
            setError(null);

            const response = await fetch('./api/products_manager.php?path=frontend');
            const result = await response.json();

            if (result.success) {
                // 只顯示啟用且有庫存的商品
                const availableProducts = result.data.filter(product =>
                    product.is_active && product.stock_status !== 'sold_out'
                );
                setProducts(availableProducts);
            } else {
                setError('載入商品失敗: ' + result.message);
            }
        } catch (err) {
            setError('載入商品時發生錯誤: ' + err.message);
            console.error('Product loading error:', err);
        } finally {
            setLoading(false);
        }
    };
    
    // 更新商品數量
    const updateProductQuantity = (productId, change) => {
        const currentQty = parseInt(orderData.products[productId]) || 0;
        const newQty = Math.max(0, currentQty + change);

        // 檢查庫存限制
        const product = products.find(p => p.id === productId);
        if (product && product.stock_status === 'limited' && newQty > product.stock_quantity) {
            window.showWarning(`⚠️ 庫存不足，最多只能選擇 ${product.stock_quantity} 條`);
            return;
        }

        setOrderData(prev => ({
            ...prev,
            products: {
                ...prev.products,
                [productId]: newQty
            }
        }));
    };
    
    // 檢查是否可以進入下一步
    const canProceed = () => {
        const totalQuantity = Object.values(orderData.products)
            .reduce((sum, qty) => sum + (parseInt(qty) || 0), 0);
        return totalQuantity > 0;
    };
    
    // 計算小計
    const getSubtotal = () => {
        return products.reduce((total, product) => {
            const qty = parseInt(orderData.products[product.id]) || 0;
            return total + (qty * product.price);
        }, 0);
    };
    
    const handleNext = () => {
        if (!canProceed()) {
            // 震動效果提醒
            const container = document.querySelector('.step-container');
            container.classList.add('shake');
            setTimeout(() => container.classList.remove('shake'), 500);
            
            window.showWarning('請至少選擇一項商品！');
            return;
        }
        onNext();
    };
    
    // 載入狀態
    if (loading) {
        return (
            <div className="space-y-4">
                <div className="text-center mb-6">
                    <h2 className="text-2xl font-bold text-gray-800 mb-2">選擇商品</h2>
                    <p className="text-gray-600">載入商品中...</p>
                </div>
                <div className="flex justify-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600"></div>
                </div>
            </div>
        );
    }

    // 錯誤狀態
    if (error) {
        return (
            <div className="space-y-4">
                <div className="text-center mb-6">
                    <h2 className="text-2xl font-bold text-gray-800 mb-2">選擇商品</h2>
                    <p className="text-red-600">{error}</p>
                </div>
                <div className="text-center">
                    <button
                        onClick={loadProducts}
                        className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded transition-colors"
                    >
                        重新載入
                    </button>
                </div>
            </div>
        );
    }

    return (
        <div className="space-y-4">
            {/* 標題 */}
            <div className="text-center mb-6">
                <h2 className="text-2xl font-bold text-gray-800 mb-2">選擇商品</h2>
                <p className="text-gray-600">請選擇您要購買的蘿蔔糕</p>
            </div>
            
            {/* 產品列表 */}
            <div className="space-y-4 scroll-area">
                {products.map(product => {
                    const quantity = parseInt(orderData.products[product.id]) || 0;
                    const isSelected = quantity > 0;
                    
                    return (
                        <div 
                            key={product.id}
                            className={`product-card p-4 ${isSelected ? 'selected' : ''}`}
                        >
                            <div className="flex items-center justify-between">
                                {/* 產品資訊 */}
                                <div className="flex-1">
                                    <div className="flex items-center mb-2">
                                        {product.image && product.image.startsWith('http') ? (
                                            <img
                                                src={product.image}
                                                alt={product.name}
                                                className="w-12 h-12 object-cover rounded mr-3"
                                                onError={(e) => {
                                                    e.target.style.display = 'none';
                                                    e.target.nextSibling.style.display = 'block';
                                                }}
                                            />
                                        ) : (
                                            <span className="text-3xl mr-3">🥮</span>
                                        )}
                                        <span className="text-3xl mr-3" style={{display: 'none'}}>🥮</span>
                                        <div>
                                            <h3 className="font-bold text-lg text-gray-800">
                                                {product.name}
                                            </h3>
                                            <p className="text-sm text-gray-600">
                                                {product.description}
                                            </p>
                                            {/* 顯示庫存狀態 */}
                                            {product.stock_status === 'limited' && (
                                                <p className="text-xs text-orange-600 font-medium">
                                                    ⚠️ 庫存有限 (剩餘 {product.stock_quantity} 條)
                                                </p>
                                            )}
                                            {product.is_vegetarian && (
                                                <span className="inline-block bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full mt-1">
                                                    🌱 素食
                                                </span>
                                            )}
                                        </div>
                                    </div>
                                    <div className="flex items-center justify-between">
                                        <span className="text-xl font-bold text-red-600">
                                            NT$ {product.price}
                                        </span>
                                        {quantity > 0 && (
                                            <span className="text-sm text-gray-600">
                                                小計: NT$ {quantity * product.price}
                                            </span>
                                        )}
                                    </div>
                                </div>
                                
                                {/* 數量控制 */}
                                <div className="flex items-center space-x-3 ml-4">
                                    <button
                                        type="button"
                                        onClick={() => updateProductQuantity(product.id, -1)}
                                        className="quantity-btn bg-gray-200 hover:bg-gray-300 text-gray-700"
                                        disabled={quantity === 0}
                                    >
                                        -
                                    </button>
                                    <span className="w-8 text-center font-bold text-lg">
                                        {quantity}
                                    </span>
                                    <button
                                        type="button"
                                        onClick={() => updateProductQuantity(product.id, 1)}
                                        className="quantity-btn bg-red-500 hover:bg-red-600 text-white"
                                    >
                                        +
                                    </button>
                                </div>
                            </div>
                        </div>
                    );
                })}
            </div>
            
            {/* 運費說明 */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <h4 className="font-bold text-blue-800 mb-2">🚚 運費說明</h4>
                <div className="text-sm text-blue-700 space-y-1">
                    <p>• 總金額滿 NT$350 免運費</p>
                    <p>• 原味蘿蔔糕滿 2 條免運費</p>
                    <p>• 精選商品（芋頭粿/台式鹹蘿蔔糕）滿 1 條免運費</p>
                    <p>• 未達免運條件運費 NT$100</p>
                </div>
            </div>
            
            {/* 訂單摘要 */}
            <div className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl p-4 border-2 border-gray-200">
                <h4 className="font-bold text-gray-800 mb-3 text-center">訂單摘要</h4>
                <div className="space-y-2">
                    <div className="flex justify-between">
                        <span className="text-gray-700">商品小計</span>
                        <span className="font-medium">NT$ {getSubtotal()}</span>
                    </div>
                    <div className="flex justify-between">
                        <span className="text-gray-700">運費</span>
                        <span className="font-medium">
                            {orderData.shipping === 0 ? '免運費' : `NT$ ${orderData.shipping}`}
                        </span>
                    </div>
                    <hr className="border-gray-300" />
                    <div className="flex justify-between text-lg font-bold">
                        <span className="text-gray-800">總計</span>
                        <span className="text-red-600">NT$ {orderData.totalAmount}</span>
                    </div>
                </div>
            </div>
            
            {/* 下一步按鈕 */}
            <div className="pt-4">
                <button
                    onClick={handleNext}
                    className={`primary-btn w-full ${
                        canProceed() 
                            ? 'bg-red-500 hover:bg-red-600 text-white' 
                            : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    }`}
                    disabled={!canProceed()}
                >
                    {canProceed() ? (
                        <>
                            下一步：填寫資料 
                            <span className="ml-2">→</span>
                        </>
                    ) : (
                        '請先選擇商品'
                    )}
                </button>
            </div>
            
            {/* 提示訊息 */}
            {!canProceed() && (
                <div className="text-center">
                    <p className="text-sm text-gray-500">
                        💡 請點擊商品右側的 <span className="bg-red-500 text-white px-2 py-1 rounded-full text-xs">+</span> 按鈕選擇商品
                    </p>
                </div>
            )}
        </div>
    );
}

// 全域導出
window.Step1ProductSelection = Step1ProductSelection;