function ProductCard({ product }) {
    try {
        // 檢查是否為素食產品（根據產品名稱判斷）
        const isVegetarian = product.name && (
            product.name.includes('純素') ||
            product.name.includes('素食') ||
            product.is_vegetarian
        );

        return (
            <div className="bg-white rounded-xl shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 overflow-hidden" data-name="product-card">
                <div className="relative">
                    <img
                        src={product.image}
                        alt={product.name}
                        className="w-full h-64 object-cover"
                        data-name="product-image"
                    />
                    {/* 素食標籤 - 左上角 */}
                    {isVegetarian && (
                        <div className="absolute top-3 left-3">
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 shadow-md">
                                🌱 素食
                            </span>
                        </div>
                    )}
                </div>
                <div className="p-6" data-name="product-info">
                    <h3 className="text-xl font-bold mb-2 text-gray-800" data-name="product-name">
                        {product.name}
                    </h3>
                    <p className="text-gray-600 mb-4" data-name="product-description">
                        {product.description}
                    </p>
                    <div className="mb-4" data-name="product-ingredients">
                        <h4 className="font-semibold text-gray-700 mb-2">成分：</h4>
                        <p className="text-gray-600">{product.ingredients}</p>
                    </div>
                    <div className="flex justify-between items-center mb-4" data-name="product-price-section">
                        <span className="text-2xl font-bold text-red-600" data-name="product-price">
                            NT$ {product.price}
                        </span>
                        <div className="text-sm text-gray-500" data-name="product-shipping">
                            {product.shippingNote}
                        </div>
                    </div>
                    {/* 立即訂購按鈕 - 水平置中 */}
                    <div className="flex justify-center">
                        <button
                            onClick={() => {
                                // 滾動到訂購表單
                                const orderForm = document.querySelector('[data-name="order-form"]');
                                if (orderForm) {
                                    orderForm.scrollIntoView({ behavior: 'smooth' });
                                }
                            }}
                            className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors"
                        >
                            立即訂購
                        </button>
                    </div>
                </div>
            </div>
        );
    } catch (error) {
        console.error('ProductCard component error:', error);
        reportError(error);
        return null;
    }
}
